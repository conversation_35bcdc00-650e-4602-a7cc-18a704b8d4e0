const { ipc<PERSON>enderer } = require('electron');

// متغيرات عامة
let currentPage = 'dashboard';
let suppliers = [];
let contracts = [];
let tables = [];
let payments = [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function () {
    initializeApp();
    setupEventListeners();
    loadDashboardData();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('تم تحميل التطبيق بنجاح');

    // تحديث عنوان النافذة
    document.title = 'إدارة عقود الموردين - جمعية المنقف';

    // تحميل البيانات الأولية
    loadAllData();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // التنقل في القائمة الجانبية
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function () {
            const page = this.getAttribute('data-page');
            navigateToPage(page);
        });
    });

    // أزرار الإضافة
    document.getElementById('add-new-btn').addEventListener('click', function () {
        if (currentPage === 'suppliers') {
            showAddSupplierModal();
        } else if (currentPage === 'contracts') {
            showAddContractModal();
        }
    });

    document.getElementById('add-supplier-btn').addEventListener('click', showAddSupplierModal);
    document.getElementById('add-contract-btn').addEventListener('click', showAddContractModal);

    // البحث والتصفية
    document.getElementById('suppliers-search').addEventListener('input', filterSuppliers);

    // البحث والتصفية للعقود
    const contractsSearch = document.getElementById('contracts-search');
    const contractTypeFilter = document.getElementById('contract-type-filter');
    const contractStatusFilter = document.getElementById('contract-status-filter');
    const contractDateFrom = document.getElementById('contract-date-from');
    const contractDateTo = document.getElementById('contract-date-to');

    if (contractsSearch) contractsSearch.addEventListener('input', filterContracts);
    if (contractTypeFilter) contractTypeFilter.addEventListener('change', filterContracts);
    if (contractStatusFilter) contractStatusFilter.addEventListener('change', filterContracts);
    if (contractDateFrom) contractDateFrom.addEventListener('change', filterContracts);
    if (contractDateTo) contractDateTo.addEventListener('change', filterContracts);

    // إغلاق النوافذ المنبثقة
    document.getElementById('modal-overlay').addEventListener('click', function (e) {
        if (e.target === this) {
            closeModal();
        }
    });
}

// التنقل بين الصفحات
function navigateToPage(page) {
    // إخفاء جميع الصفحات
    const pages = document.querySelectorAll('.page');
    pages.forEach(p => p.classList.remove('active'));

    // إزالة الحالة النشطة من جميع عناصر القائمة
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => item.classList.remove('active'));

    // إظهار الصفحة المحددة
    document.getElementById(page + '-page').classList.add('active');
    document.querySelector(`[data-page="${page}"]`).classList.add('active');

    // تحديث عنوان الصفحة
    updatePageTitle(page);

    // تحديث الصفحة الحالية
    currentPage = page;

    // تحميل بيانات الصفحة
    loadPageData(page);
}

// تحديث عنوان الصفحة
function updatePageTitle(page) {
    const titles = {
        'dashboard': 'لوحة التحكم',
        'suppliers': 'إدارة الموردين',
        'contracts': 'إدارة العقود',
        'tables': 'إدارة الطبليات',
        'payments': 'إدارة المدفوعات',
        'reports': 'التقارير',
        'search': 'البحث المتقدم'
    };

    const subtitles = {
        'dashboard': 'نظرة عامة على حالة العقود والمدفوعات',
        'suppliers': 'إضافة وإدارة بيانات الموردين',
        'contracts': 'متابعة وإدارة عقود الإيجار',
        'tables': 'إدارة الطبليات والمساحات',
        'payments': 'متابعة المدفوعات والمستحقات',
        'reports': 'تقارير دورية وإحصائيات',
        'search': 'البحث المتقدم في البيانات'
    };

    document.getElementById('page-title').textContent = titles[page] || 'صفحة غير معروفة';
    document.getElementById('page-subtitle').textContent = subtitles[page] || '';
}

// تحميل جميع البيانات
async function loadAllData() {
    try {
        await Promise.all([
            loadSuppliers(),
            loadContracts(),
            loadTables(),
            loadPayments()
        ]);

        updateDashboardStats();
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showAlert('حدث خطأ في تحميل البيانات', 'danger');
    }
}

// تحميل بيانات الموردين
async function loadSuppliers() {
    await suppliersManager.loadSuppliers();
}

// تحميل بيانات العقود
async function loadContracts() {
    await contractsManager.loadContracts();
}

// تحميل بيانات الطبليات
async function loadTables() {
    try {
        const query = `
            SELECT t.*, c.contract_type, s.name as supplier_name 
            FROM tables_spaces t 
            LEFT JOIN contracts c ON t.contract_id = c.id 
            LEFT JOIN suppliers s ON c.supplier_id = s.id 
            ORDER BY t.table_number
        `;
        tables = await ipcRenderer.invoke('db-query', query);
    } catch (error) {
        console.error('خطأ في تحميل الطبليات:', error);
    }
}

// تحميل بيانات المدفوعات
async function loadPayments() {
    try {
        const query = `
            SELECT p.*, s.name as supplier_name, c.contract_type 
            FROM payments p 
            LEFT JOIN contracts c ON p.contract_id = c.id 
            LEFT JOIN suppliers s ON c.supplier_id = s.id 
            ORDER BY p.due_date DESC
        `;
        payments = await ipcRenderer.invoke('db-query', query);
    } catch (error) {
        console.error('خطأ في تحميل المدفوعات:', error);
    }
}

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    document.getElementById('total-suppliers').textContent = suppliers.length;
    document.getElementById('active-contracts').textContent = contracts.filter(c => c.status === 'نشط').length;
    document.getElementById('rented-tables').textContent = tables.filter(t => t.status === 'مؤجرة').length;
    document.getElementById('pending-payments').textContent = payments.filter(p => p.status === 'مستحق').length;
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
    await loadExpiringContracts();
    await loadContractsDistribution();
    await updateDashboardStats();
}

// تحميل توزيع العقود
async function loadContractsDistribution() {
    try {
        const distribution = await ipcRenderer.invoke('db-query', `
            SELECT
                contract_type,
                COUNT(*) as count,
                COUNT(CASE WHEN status = 'نشط' THEN 1 END) as active_count
            FROM contracts
            GROUP BY contract_type
        `);

        const chartContainer = document.getElementById('contracts-distribution-chart');

        if (distribution.length === 0) {
            chartContainer.innerHTML = '<p>لا توجد بيانات عقود لعرضها</p>';
            return;
        }

        let chartHTML = '<div class="distribution-chart">';
        distribution.forEach(item => {
            const percentage = distribution.length > 0 ? (item.count / distribution.reduce((sum, d) => sum + d.count, 0) * 100).toFixed(1) : 0;
            chartHTML += `
                <div class="chart-item">
                    <div class="chart-bar">
                        <div class="bar-fill type-${item.contract_type}" style="width: ${percentage}%"></div>
                    </div>
                    <div class="chart-label">
                        <span class="type-name">${item.contract_type}</span>
                        <span class="type-count">${item.active_count}/${item.count}</span>
                    </div>
                </div>
            `;
        });
        chartHTML += '</div>';

        chartContainer.innerHTML = chartHTML;

    } catch (error) {
        console.error('خطأ في تحميل توزيع العقود:', error);
    }
}

// تحديث إحصائيات لوحة التحكم
async function updateDashboardStats() {
    try {
        // تحديث إحصائيات الموردين
        const suppliersCount = await ipcRenderer.invoke('db-get', 'SELECT COUNT(*) as count FROM suppliers');
        document.getElementById('total-suppliers').textContent = suppliersCount.count || 0;

        // تحديث إحصائيات العقود النشطة
        const activeContractsCount = await ipcRenderer.invoke('db-get', 'SELECT COUNT(*) as count FROM contracts WHERE status = "نشط"');
        document.getElementById('active-contracts').textContent = activeContractsCount.count || 0;

        // تحديث إحصائيات الطبليات المؤجرة
        const tablesCount = await ipcRenderer.invoke('db-get', 'SELECT COUNT(*) as count FROM contracts WHERE contract_type = "طبليات" AND status = "نشط"');
        document.getElementById('rented-tables').textContent = tablesCount.count || 0;

        // تحديث المدفوعات المستحقة (مؤقت)
        document.getElementById('pending-payments').textContent = '0';

    } catch (error) {
        console.error('خطأ في تحديث إحصائيات لوحة التحكم:', error);
    }
}

// تحميل العقود المنتهية قريباً
async function loadExpiringContracts() {
    try {
        const query = `
            SELECT c.*, s.name as supplier_name 
            FROM contracts c 
            LEFT JOIN suppliers s ON c.supplier_id = s.id 
            WHERE c.status = 'نشط' 
            AND date(c.end_date) <= date('now', '+30 days')
            ORDER BY c.end_date ASC
            LIMIT 5
        `;
        const expiringContracts = await ipcRenderer.invoke('db-query', query);
        renderExpiringContractsTable(expiringContracts);
    } catch (error) {
        console.error('خطأ في تحميل العقود المنتهية:', error);
    }
}

// تحميل المدفوعات المتأخرة
async function loadOverduePayments() {
    try {
        const query = `
            SELECT p.*, s.name as supplier_name,
            julianday('now') - julianday(p.due_date) as days_overdue
            FROM payments p 
            LEFT JOIN contracts c ON p.contract_id = c.id 
            LEFT JOIN suppliers s ON c.supplier_id = s.id 
            WHERE p.status IN ('مستحق', 'متأخر') 
            AND date(p.due_date) < date('now')
            ORDER BY p.due_date ASC
            LIMIT 5
        `;
        const overduePayments = await ipcRenderer.invoke('db-query', query);
        renderOverduePaymentsTable(overduePayments);
    } catch (error) {
        console.error('خطأ في تحميل المدفوعات المتأخرة:', error);
    }
}

// عرض جدول الموردين (تم نقلها إلى suppliersManager)
function renderSuppliersTable() {
    // هذه الدالة تم نقلها إلى suppliersManager.renderSuppliersTable()
    // يتم استدعاؤها تلقائياً من loadSuppliers()
}

// عرض جدول العقود (تم نقلها إلى contractsManager)
function renderContractsTable() {
    // هذه الدالة تم نقلها إلى contractsManager.renderContractsTable()
    // يتم استدعاؤها تلقائياً من loadContracts()
}

// عرض جدول العقود المنتهية قريباً
function renderExpiringContractsTable(contracts) {
    const tbody = document.querySelector('#expiring-contracts-table tbody');
    tbody.innerHTML = '';

    if (contracts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">لا توجد عقود منتهية قريباً</td></tr>';
        return;
    }

    contracts.forEach(contract => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${contract.supplier_name}</td>
            <td>${contract.contract_type}</td>
            <td>${formatDate(contract.end_date)}</td>
            <td><span class="status ${getStatusClass(contract.status)}">${contract.status}</span></td>
        `;
        tbody.appendChild(row);
    });
}

// عرض جدول المدفوعات المتأخرة
function renderOverduePaymentsTable(payments) {
    const tbody = document.querySelector('#overdue-payments-table tbody');
    tbody.innerHTML = '';

    if (payments.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">لا توجد مدفوعات متأخرة</td></tr>';
        return;
    }

    payments.forEach(payment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${payment.supplier_name}</td>
            <td>${formatCurrency(payment.amount)}</td>
            <td>${formatDate(payment.due_date)}</td>
            <td>${Math.floor(payment.days_overdue)} يوم</td>
        `;
        tbody.appendChild(row);
    });
}

// تحميل بيانات الصفحة
function loadPageData(page) {
    switch (page) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'suppliers':
            renderSuppliersTable();
            break;
        case 'contracts':
            renderContractsTable();
            break;
        // يمكن إضافة المزيد من الصفحات هنا
    }
}

// دوال مساعدة
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatCurrency(amount) {
    if (!amount) return '-';
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'KWD'
    }).format(amount);
}

function getStatusClass(status) {
    switch (status) {
        case 'نشط': return 'active';
        case 'منتهي': return 'inactive';
        case 'معلق': return 'pending';
        default: return '';
    }
}

function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;

    const pageContent = document.querySelector('.page-content');
    pageContent.insertBefore(alertDiv, pageContent.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// إظهار النافذة المنبثقة
function showModal(content) {
    const modalContent = document.getElementById('modal-content');
    modalContent.innerHTML = content;
    document.getElementById('modal-overlay').classList.add('active');
}

// إغلاق النافذة المنبثقة
function closeModal() {
    document.getElementById('modal-overlay').classList.remove('active');
}

// إظهار نافذة إضافة مورد
function showAddSupplierModal() {
    const content = `
        <h3>إضافة مورد جديد</h3>
        <form id="supplier-form">
            <div class="form-group">
                <label for="supplier-name">اسم المورد *</label>
                <input type="text" id="supplier-name" name="name" required>
            </div>
            <div class="form-group">
                <label for="supplier-phone">رقم الهاتف</label>
                <input type="tel" id="supplier-phone" name="phone">
            </div>
            <div class="form-group">
                <label for="supplier-email">البريد الإلكتروني</label>
                <input type="email" id="supplier-email" name="email">
            </div>
            <div class="form-group">
                <label for="supplier-address">العنوان</label>
                <textarea id="supplier-address" name="address" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label for="supplier-activity">نوع النشاط *</label>
                <select id="supplier-activity" name="activity_type" required>
                    <option value="">اختر نوع النشاط</option>
                    <option value="استهلاكي">استهلاكي</option>
                    <option value="غذائي">غذائي</option>
                </select>
            </div>
            <div class="form-group">
                <label for="supplier-notes">ملاحظات</label>
                <textarea id="supplier-notes" name="notes" rows="3"></textarea>
            </div>
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                <button type="submit" class="btn btn-primary">حفظ</button>
            </div>
        </form>
    `;

    showModal(content);

    // إضافة مستمع الحدث للنموذج
    document.getElementById('supplier-form').addEventListener('submit', handleAddSupplier);
}

// إظهار نافذة إضافة المورد (دالة مساعدة)
function showAddSupplierModal() {
    showAddSupplierForm();
}

// إظهار نافذة إضافة عقد
function showAddContractModal() {
    contractsManager.showAddContractModal();
}



// معالج إضافة مورد
async function handleAddSupplier(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const supplierData = Object.fromEntries(formData.entries());

    const success = await suppliersManager.addSupplier(supplierData);
    if (success) {
        closeModal();
        updateDashboardStats();
    }
}

// معالج إضافة عقد
async function handleAddContract(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const contractData = Object.fromEntries(formData.entries());

    const contractId = await contractsManager.addContract(contractData);
    if (contractId) {
        closeModal();
        updateDashboardStats();
    }
}

// تصفية الموردين
function filterSuppliers() {
    const searchTerm = document.getElementById('suppliers-search').value.toLowerCase();
    const rows = document.querySelectorAll('#suppliers-table tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

// تصفية العقود
function filterContracts() {
    const searchTerm = document.getElementById('contracts-search')?.value || '';
    const contractType = document.getElementById('contract-type-filter')?.value || '';
    const status = document.getElementById('contract-status-filter')?.value || '';
    const dateFrom = document.getElementById('contract-date-from')?.value || '';
    const dateTo = document.getElementById('contract-date-to')?.value || '';

    contractsManager.filterContracts(searchTerm, contractType, status, dateFrom, dateTo);
}

// حذف مورد
async function deleteSupplier(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المورد؟ سيتم حذف جميع العقود المرتبطة به أيضاً.')) {
        return;
    }

    try {
        // حذف العقود المرتبطة أولاً
        await ipcRenderer.invoke('db-run', 'DELETE FROM contracts WHERE supplier_id = ?', [id]);

        // ثم حذف المورد
        await ipcRenderer.invoke('db-run', 'DELETE FROM suppliers WHERE id = ?', [id]);

        showAlert('تم حذف المورد بنجاح', 'success');
        await loadAllData();

    } catch (error) {
        console.error('خطأ في حذف المورد:', error);
        showAlert('حدث خطأ في حذف المورد', 'danger');
    }
}

// حذف عقد
async function deleteContract(id) {
    if (!confirm('هل أنت متأكد من حذف هذا العقد؟')) {
        return;
    }

    try {
        await ipcRenderer.invoke('db-run', 'DELETE FROM contracts WHERE id = ?', [id]);

        showAlert('تم حذف العقد بنجاح', 'success');
        await loadContracts();
        updateDashboardStats();

    } catch (error) {
        console.error('خطأ في حذف العقد:', error);
        showAlert('حدث خطأ في حذف العقد', 'danger');
    }
}

// تعديل مورد (سيتم تطويرها لاحقاً)
function editSupplier(id) {
    showAlert('وظيفة التعديل قيد التطوير', 'warning');
}

// تعديل عقد (سيتم تطويرها لاحقاً)
function editContract(id) {
    showAlert('وظيفة التعديل قيد التطوير', 'warning');
}
