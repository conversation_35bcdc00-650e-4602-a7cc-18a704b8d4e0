// إدارة الموردين - وظائف متقدمة

class SuppliersManager {
    constructor() {
        this.suppliers = [];
        this.currentEditingId = null;
    }

    // تحميل جميع الموردين
    async loadSuppliers() {
        try {
            this.suppliers = await ipcRenderer.invoke('db-query', 'SELECT * FROM suppliers ORDER BY name');
            this.renderSuppliersTable();
            this.updateSuppliersStats();
        } catch (error) {
            console.error('خطأ في تحميل الموردين:', error);
            showAlert('حدث خطأ في تحميل بيانات الموردين', 'danger');
        }
    }

    // عرض جدول الموردين مع تحسينات
    renderSuppliersTable() {
        const tbody = document.querySelector('#suppliers-table tbody');
        tbody.innerHTML = '';

        if (this.suppliers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px;">
                        <i class="fas fa-users" style="font-size: 3rem; color: #ccc; margin-bottom: 10px;"></i>
                        <p>لا توجد موردين مسجلين بعد</p>
                        <button class="btn btn-primary" onclick="showAddSupplierModal()">
                            <i class="fas fa-plus"></i> إضافة أول مورد
                        </button>
                    </td>
                </tr>
            `;
            return;
        }

        this.suppliers.forEach(supplier => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="supplier-info">
                        <strong>${supplier.name}</strong>
                        ${supplier.email ? `<br><small class="text-muted">${supplier.email}</small>` : ''}
                    </div>
                </td>
                <td>
                    <div class="contact-info">
                        ${supplier.phone ? `<i class="fas fa-phone"></i> ${supplier.phone}` : '-'}
                    </div>
                </td>
                <td>
                    <span class="activity-badge activity-${supplier.activity_type}">
                        ${supplier.activity_type || '-'}
                    </span>
                </td>
                <td>${this.formatDate(supplier.registration_date)}</td>
                <td>
                    <span class="status ${supplier.status === 'نشط' ? 'active' : 'inactive'}">
                        ${supplier.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="suppliersManager.editSupplier(${supplier.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="suppliersManager.viewSupplierDetails(${supplier.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="suppliersManager.toggleSupplierStatus(${supplier.id})" title="تغيير الحالة">
                            <i class="fas fa-toggle-on"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="suppliersManager.deleteSupplier(${supplier.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // إضافة مورد جديد
    async addSupplier(supplierData) {
        try {
            // التحقق من صحة البيانات
            if (!this.validateSupplierData(supplierData)) {
                return false;
            }

            // التحقق من عدم تكرار الاسم
            const existingSupplier = await ipcRenderer.invoke('db-get', 
                'SELECT id FROM suppliers WHERE name = ?', [supplierData.name]);
            
            if (existingSupplier) {
                showAlert('يوجد مورد بنفس الاسم مسجل مسبقاً', 'warning');
                return false;
            }

            const query = `
                INSERT INTO suppliers (name, phone, email, address, activity_type, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            `;
            
            await ipcRenderer.invoke('db-run', query, [
                supplierData.name,
                supplierData.phone,
                supplierData.email,
                supplierData.address,
                supplierData.activity_type,
                supplierData.notes
            ]);
            
            showAlert('تم إضافة المورد بنجاح', 'success');
            await this.loadSuppliers();
            return true;
            
        } catch (error) {
            console.error('خطأ في إضافة المورد:', error);
            showAlert('حدث خطأ في إضافة المورد', 'danger');
            return false;
        }
    }

    // تعديل مورد
    async editSupplier(id) {
        try {
            const supplier = await ipcRenderer.invoke('db-get', 'SELECT * FROM suppliers WHERE id = ?', [id]);
            
            if (!supplier) {
                showAlert('لم يتم العثور على المورد', 'danger');
                return;
            }

            this.currentEditingId = id;
            this.showEditSupplierModal(supplier);
            
        } catch (error) {
            console.error('خطأ في تحميل بيانات المورد:', error);
            showAlert('حدث خطأ في تحميل بيانات المورد', 'danger');
        }
    }

    // إظهار نافذة تعديل المورد
    showEditSupplierModal(supplier) {
        const content = `
            <h3>تعديل بيانات المورد</h3>
            <form id="edit-supplier-form">
                <div class="form-group">
                    <label for="edit-supplier-name">اسم المورد *</label>
                    <input type="text" id="edit-supplier-name" name="name" value="${supplier.name}" required>
                </div>
                <div class="form-group">
                    <label for="edit-supplier-phone">رقم الهاتف</label>
                    <input type="tel" id="edit-supplier-phone" name="phone" value="${supplier.phone || ''}">
                </div>
                <div class="form-group">
                    <label for="edit-supplier-email">البريد الإلكتروني</label>
                    <input type="email" id="edit-supplier-email" name="email" value="${supplier.email || ''}">
                </div>
                <div class="form-group">
                    <label for="edit-supplier-address">العنوان</label>
                    <textarea id="edit-supplier-address" name="address" rows="3">${supplier.address || ''}</textarea>
                </div>
                <div class="form-group">
                    <label for="edit-supplier-activity">نوع النشاط *</label>
                    <select id="edit-supplier-activity" name="activity_type" required>
                        <option value="">اختر نوع النشاط</option>
                        <option value="استهلاكي" ${supplier.activity_type === 'استهلاكي' ? 'selected' : ''}>استهلاكي</option>
                        <option value="غذائي" ${supplier.activity_type === 'غذائي' ? 'selected' : ''}>غذائي</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-supplier-status">الحالة</label>
                    <select id="edit-supplier-status" name="status">
                        <option value="نشط" ${supplier.status === 'نشط' ? 'selected' : ''}>نشط</option>
                        <option value="غير نشط" ${supplier.status === 'غير نشط' ? 'selected' : ''}>غير نشط</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-supplier-notes">ملاحظات</label>
                    <textarea id="edit-supplier-notes" name="notes" rows="3">${supplier.notes || ''}</textarea>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        `;
        
        showModal(content);
        
        // إضافة مستمع الحدث للنموذج
        document.getElementById('edit-supplier-form').addEventListener('submit', (e) => {
            this.handleEditSupplier(e);
        });
    }

    // معالج تعديل المورد
    async handleEditSupplier(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const supplierData = Object.fromEntries(formData.entries());
        
        try {
            // التحقق من صحة البيانات
            if (!this.validateSupplierData(supplierData)) {
                return;
            }

            const query = `
                UPDATE suppliers 
                SET name = ?, phone = ?, email = ?, address = ?, activity_type = ?, status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            
            await ipcRenderer.invoke('db-run', query, [
                supplierData.name,
                supplierData.phone,
                supplierData.email,
                supplierData.address,
                supplierData.activity_type,
                supplierData.status,
                supplierData.notes,
                this.currentEditingId
            ]);
            
            showAlert('تم تحديث بيانات المورد بنجاح', 'success');
            closeModal();
            await this.loadSuppliers();
            this.currentEditingId = null;
            
        } catch (error) {
            console.error('خطأ في تحديث المورد:', error);
            showAlert('حدث خطأ في تحديث بيانات المورد', 'danger');
        }
    }

    // عرض تفاصيل المورد
    async viewSupplierDetails(id) {
        try {
            const supplier = await ipcRenderer.invoke('db-get', 'SELECT * FROM suppliers WHERE id = ?', [id]);
            
            if (!supplier) {
                showAlert('لم يتم العثور على المورد', 'danger');
                return;
            }

            // الحصول على عدد العقود
            const contractsCount = await ipcRenderer.invoke('db-get', 
                'SELECT COUNT(*) as count FROM contracts WHERE supplier_id = ?', [id]);

            const content = `
                <h3>تفاصيل المورد</h3>
                <div class="supplier-details">
                    <div class="detail-row">
                        <strong>الاسم:</strong> ${supplier.name}
                    </div>
                    <div class="detail-row">
                        <strong>رقم الهاتف:</strong> ${supplier.phone || 'غير محدد'}
                    </div>
                    <div class="detail-row">
                        <strong>البريد الإلكتروني:</strong> ${supplier.email || 'غير محدد'}
                    </div>
                    <div class="detail-row">
                        <strong>العنوان:</strong> ${supplier.address || 'غير محدد'}
                    </div>
                    <div class="detail-row">
                        <strong>نوع النشاط:</strong> 
                        <span class="activity-badge activity-${supplier.activity_type}">${supplier.activity_type}</span>
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ التسجيل:</strong> ${this.formatDate(supplier.registration_date)}
                    </div>
                    <div class="detail-row">
                        <strong>الحالة:</strong> 
                        <span class="status ${supplier.status === 'نشط' ? 'active' : 'inactive'}">${supplier.status}</span>
                    </div>
                    <div class="detail-row">
                        <strong>عدد العقود:</strong> ${contractsCount.count}
                    </div>
                    ${supplier.notes ? `
                        <div class="detail-row">
                            <strong>ملاحظات:</strong> ${supplier.notes}
                        </div>
                    ` : ''}
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="suppliersManager.editSupplier(${supplier.id}); closeModal();">تعديل</button>
                </div>
            `;
            
            showModal(content);
            
        } catch (error) {
            console.error('خطأ في عرض تفاصيل المورد:', error);
            showAlert('حدث خطأ في عرض تفاصيل المورد', 'danger');
        }
    }

    // تغيير حالة المورد
    async toggleSupplierStatus(id) {
        try {
            const supplier = await ipcRenderer.invoke('db-get', 'SELECT status FROM suppliers WHERE id = ?', [id]);
            
            if (!supplier) {
                showAlert('لم يتم العثور على المورد', 'danger');
                return;
            }

            const newStatus = supplier.status === 'نشط' ? 'غير نشط' : 'نشط';
            
            await ipcRenderer.invoke('db-run', 
                'UPDATE suppliers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', 
                [newStatus, id]);
            
            showAlert(`تم تغيير حالة المورد إلى: ${newStatus}`, 'success');
            await this.loadSuppliers();
            
        } catch (error) {
            console.error('خطأ في تغيير حالة المورد:', error);
            showAlert('حدث خطأ في تغيير حالة المورد', 'danger');
        }
    }

    // حذف مورد
    async deleteSupplier(id) {
        try {
            const supplier = await ipcRenderer.invoke('db-get', 'SELECT name FROM suppliers WHERE id = ?', [id]);
            
            if (!supplier) {
                showAlert('لم يتم العثور على المورد', 'danger');
                return;
            }

            // التحقق من وجود عقود مرتبطة
            const contractsCount = await ipcRenderer.invoke('db-get', 
                'SELECT COUNT(*) as count FROM contracts WHERE supplier_id = ?', [id]);

            let confirmMessage = `هل أنت متأكد من حذف المورد "${supplier.name}"؟`;
            
            if (contractsCount.count > 0) {
                confirmMessage += `\n\nتحذير: يوجد ${contractsCount.count} عقد مرتبط بهذا المورد وسيتم حذفها أيضاً.`;
            }

            if (!confirm(confirmMessage)) {
                return;
            }
            
            // حذف العقود المرتبطة أولاً
            await ipcRenderer.invoke('db-run', 'DELETE FROM contracts WHERE supplier_id = ?', [id]);
            
            // ثم حذف المورد
            await ipcRenderer.invoke('db-run', 'DELETE FROM suppliers WHERE id = ?', [id]);
            
            showAlert('تم حذف المورد بنجاح', 'success');
            await this.loadSuppliers();
            
        } catch (error) {
            console.error('خطأ في حذف المورد:', error);
            showAlert('حدث خطأ في حذف المورد', 'danger');
        }
    }

    // التحقق من صحة بيانات المورد
    validateSupplierData(data) {
        if (!data.name || data.name.trim().length < 2) {
            showAlert('يجب إدخال اسم المورد (حد أدنى حرفين)', 'warning');
            return false;
        }

        if (!data.activity_type) {
            showAlert('يجب اختيار نوع النشاط', 'warning');
            return false;
        }

        if (data.email && !this.isValidEmail(data.email)) {
            showAlert('البريد الإلكتروني غير صحيح', 'warning');
            return false;
        }

        if (data.phone && !this.isValidPhone(data.phone)) {
            showAlert('رقم الهاتف غير صحيح', 'warning');
            return false;
        }

        return true;
    }

    // التحقق من صحة البريد الإلكتروني
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // التحقق من صحة رقم الهاتف
    isValidPhone(phone) {
        const phoneRegex = /^[0-9+\-\s()]{8,15}$/;
        return phoneRegex.test(phone);
    }

    // تنسيق التاريخ
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    // تحديث إحصائيات الموردين
    updateSuppliersStats() {
        const totalSuppliers = this.suppliers.length;
        const activeSuppliers = this.suppliers.filter(s => s.status === 'نشط').length;
        const consumerSuppliers = this.suppliers.filter(s => s.activity_type === 'استهلاكي').length;
        const foodSuppliers = this.suppliers.filter(s => s.activity_type === 'غذائي').length;

        // تحديث العناصر في الواجهة إذا كانت موجودة
        const statsElements = {
            'total-suppliers': totalSuppliers,
            'active-suppliers': activeSuppliers,
            'consumer-suppliers': consumerSuppliers,
            'food-suppliers': foodSuppliers
        };

        Object.entries(statsElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    // تصفية الموردين
    filterSuppliers(searchTerm = '', activityType = '', status = '') {
        const rows = document.querySelectorAll('#suppliers-table tbody tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length < 6) return; // تجاهل الصفوف الفارغة

            const name = cells[0].textContent.toLowerCase();
            const activity = cells[2].textContent.toLowerCase();
            const supplierStatus = cells[4].textContent.toLowerCase();

            const matchesSearch = !searchTerm || name.includes(searchTerm.toLowerCase());
            const matchesActivity = !activityType || activity.includes(activityType);
            const matchesStatus = !status || supplierStatus.includes(status);

            row.style.display = (matchesSearch && matchesActivity && matchesStatus) ? '' : 'none';
        });
    }
}

// إنشاء مثيل من مدير الموردين
const suppliersManager = new SuppliersManager();
