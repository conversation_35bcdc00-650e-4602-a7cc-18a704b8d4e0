// نظام التقارير - تقارير شاملة ومتقدمة

class ReportsManager {
    constructor() {
        this.reportData = {};
    }

    // تحميل بيانات التقارير
    async loadReportData() {
        try {
            // تحميل إحصائيات الموردين
            const suppliersStats = await ipcRenderer.invoke('db-query', `
                SELECT 
                    COUNT(*) as total_suppliers,
                    COUNT(CASE WHEN status = 'نشط' THEN 1 END) as active_suppliers,
                    COUNT(CASE WHEN activity_type = 'استهلاكي' THEN 1 END) as consumer_suppliers,
                    COUNT(CASE WHEN activity_type = 'غذائي' THEN 1 END) as food_suppliers
                FROM suppliers
            `);

            // تحميل إحصائيات العقود
            const contractsStats = await ipcRenderer.invoke('db-query', `
                SELECT 
                    COUNT(*) as total_contracts,
                    COUNT(CASE WHEN status = 'نشط' THEN 1 END) as active_contracts,
                    COUNT(CASE WHEN status = 'منتهي' THEN 1 END) as expired_contracts,
                    COUNT(CASE WHEN status = 'معلق' THEN 1 END) as suspended_contracts,
                    SUM(CASE WHEN status = 'نشط' AND total_amount IS NOT NULL THEN total_amount ELSE 0 END) as total_active_amount,
                    COUNT(CASE WHEN contract_type = 'طبليات' THEN 1 END) as tables_contracts,
                    COUNT(CASE WHEN contract_type = 'قواطع' THEN 1 END) as partitions_contracts,
                    COUNT(CASE WHEN contract_type = 'جندولات' THEN 1 END) as gondolas_contracts,
                    COUNT(CASE WHEN contract_type = 'استنادات' THEN 1 END) as stands_contracts
                FROM contracts
            `);

            // تحميل العقود المنتهية قريباً (خلال 30 يوم)
            const expiringContracts = await ipcRenderer.invoke('db-query', `
                SELECT c.*, s.name as supplier_name, s.phone
                FROM contracts c
                LEFT JOIN suppliers s ON c.supplier_id = s.id
                WHERE c.status = 'نشط' 
                AND date(c.end_date) BETWEEN date('now') AND date('now', '+30 days')
                ORDER BY c.end_date ASC
            `);

            // تحميل العقود حسب الشهر (آخر 12 شهر)
            const monthlyContracts = await ipcRenderer.invoke('db-query', `
                SELECT 
                    strftime('%Y-%m', start_date) as month,
                    COUNT(*) as contracts_count,
                    SUM(CASE WHEN total_amount IS NOT NULL THEN total_amount ELSE 0 END) as total_amount
                FROM contracts
                WHERE start_date >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', start_date)
                ORDER BY month DESC
            `);

            // تحميل أفضل الموردين (حسب عدد العقود)
            const topSuppliers = await ipcRenderer.invoke('db-query', `
                SELECT 
                    s.name,
                    s.activity_type,
                    COUNT(c.id) as contracts_count,
                    SUM(CASE WHEN c.total_amount IS NOT NULL THEN c.total_amount ELSE 0 END) as total_amount
                FROM suppliers s
                LEFT JOIN contracts c ON s.id = c.supplier_id
                WHERE s.status = 'نشط'
                GROUP BY s.id, s.name, s.activity_type
                HAVING contracts_count > 0
                ORDER BY contracts_count DESC, total_amount DESC
                LIMIT 10
            `);

            this.reportData = {
                suppliers: suppliersStats[0] || {},
                contracts: contractsStats[0] || {},
                expiringContracts: expiringContracts || [],
                monthlyContracts: monthlyContracts || [],
                topSuppliers: topSuppliers || []
            };

        } catch (error) {
            console.error('خطأ في تحميل بيانات التقارير:', error);
            showAlert('حدث خطأ في تحميل بيانات التقارير', 'danger');
        }
    }

    // عرض تقرير شامل
    async showComprehensiveReport() {
        await this.loadReportData();
        
        const content = `
            <div class="report-header">
                <h2>التقرير الشامل - جمعية المنقف</h2>
                <p class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>
            
            <div class="report-content">
                <!-- إحصائيات عامة -->
                <div class="report-section">
                    <h3><i class="fas fa-chart-bar"></i> الإحصائيات العامة</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${this.reportData.suppliers.total_suppliers || 0}</div>
                            <div class="stat-label">إجمالي الموردين</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${this.reportData.suppliers.active_suppliers || 0}</div>
                            <div class="stat-label">الموردين النشطين</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${this.reportData.contracts.total_contracts || 0}</div>
                            <div class="stat-label">إجمالي العقود</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${this.reportData.contracts.active_contracts || 0}</div>
                            <div class="stat-label">العقود النشطة</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${this.formatCurrency(this.reportData.contracts.total_active_amount || 0)}</div>
                            <div class="stat-label">إجمالي قيمة العقود النشطة</div>
                        </div>
                    </div>
                </div>

                <!-- توزيع الموردين -->
                <div class="report-section">
                    <h3><i class="fas fa-users"></i> توزيع الموردين حسب نوع النشاط</h3>
                    <div class="chart-container">
                        <div class="pie-chart">
                            <div class="chart-item">
                                <span class="chart-color consumer"></span>
                                <span>استهلاكي: ${this.reportData.suppliers.consumer_suppliers || 0}</span>
                            </div>
                            <div class="chart-item">
                                <span class="chart-color food"></span>
                                <span>غذائي: ${this.reportData.suppliers.food_suppliers || 0}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- توزيع العقود -->
                <div class="report-section">
                    <h3><i class="fas fa-file-contract"></i> توزيع العقود حسب النوع</h3>
                    <div class="chart-container">
                        <div class="bar-chart">
                            <div class="chart-bar">
                                <div class="bar-label">طبليات</div>
                                <div class="bar-value">${this.reportData.contracts.tables_contracts || 0}</div>
                            </div>
                            <div class="chart-bar">
                                <div class="bar-label">قواطع</div>
                                <div class="bar-value">${this.reportData.contracts.partitions_contracts || 0}</div>
                            </div>
                            <div class="chart-bar">
                                <div class="bar-label">جندولات</div>
                                <div class="bar-value">${this.reportData.contracts.gondolas_contracts || 0}</div>
                            </div>
                            <div class="chart-bar">
                                <div class="bar-label">استنادات</div>
                                <div class="bar-value">${this.reportData.contracts.stands_contracts || 0}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العقود المنتهية قريباً -->
                <div class="report-section">
                    <h3><i class="fas fa-exclamation-triangle"></i> العقود المنتهية خلال 30 يوم</h3>
                    ${this.renderExpiringContractsTable()}
                </div>

                <!-- أفضل الموردين -->
                <div class="report-section">
                    <h3><i class="fas fa-trophy"></i> أفضل الموردين</h3>
                    ${this.renderTopSuppliersTable()}
                </div>

                <!-- الاتجاه الشهري -->
                <div class="report-section">
                    <h3><i class="fas fa-chart-line"></i> اتجاه العقود الشهري</h3>
                    ${this.renderMonthlyTrend()}
                </div>
            </div>

            <div class="report-actions">
                <button class="btn btn-primary" onclick="reportsManager.printReport()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
                <button class="btn btn-success" onclick="reportsManager.exportReport('excel')">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button class="btn btn-info" onclick="reportsManager.exportReport('pdf')">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
            </div>
        `;

        showModal(content, 'large');
    }

    // عرض جدول العقود المنتهية قريباً
    renderExpiringContractsTable() {
        if (this.reportData.expiringContracts.length === 0) {
            return '<p class="text-muted">لا توجد عقود منتهية خلال الفترة القادمة</p>';
        }

        let tableHTML = `
            <div class="table-container">
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>المورد</th>
                            <th>نوع العقد</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الأيام المتبقية</th>
                            <th>رقم الهاتف</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        this.reportData.expiringContracts.forEach(contract => {
            const daysRemaining = this.getDaysRemaining(contract.end_date);
            tableHTML += `
                <tr>
                    <td>${contract.supplier_name || 'غير محدد'}</td>
                    <td>${contract.contract_type}</td>
                    <td>${this.formatDate(contract.end_date)}</td>
                    <td class="${daysRemaining <= 7 ? 'text-danger' : daysRemaining <= 15 ? 'text-warning' : ''}">${daysRemaining} يوم</td>
                    <td>${contract.phone || '-'}</td>
                </tr>
            `;
        });

        tableHTML += '</tbody></table></div>';
        return tableHTML;
    }

    // عرض جدول أفضل الموردين
    renderTopSuppliersTable() {
        if (this.reportData.topSuppliers.length === 0) {
            return '<p class="text-muted">لا توجد بيانات موردين</p>';
        }

        let tableHTML = `
            <div class="table-container">
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>الترتيب</th>
                            <th>اسم المورد</th>
                            <th>نوع النشاط</th>
                            <th>عدد العقود</th>
                            <th>إجمالي القيمة</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        this.reportData.topSuppliers.forEach((supplier, index) => {
            tableHTML += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${supplier.name}</td>
                    <td>${supplier.activity_type || '-'}</td>
                    <td>${supplier.contracts_count}</td>
                    <td>${this.formatCurrency(supplier.total_amount)}</td>
                </tr>
            `;
        });

        tableHTML += '</tbody></table></div>';
        return tableHTML;
    }

    // عرض الاتجاه الشهري
    renderMonthlyTrend() {
        if (this.reportData.monthlyContracts.length === 0) {
            return '<p class="text-muted">لا توجد بيانات شهرية</p>';
        }

        let chartHTML = '<div class="monthly-chart">';
        
        this.reportData.monthlyContracts.forEach(month => {
            const monthName = this.formatMonth(month.month);
            chartHTML += `
                <div class="month-item">
                    <div class="month-label">${monthName}</div>
                    <div class="month-bar" style="height: ${Math.max(month.contracts_count * 10, 20)}px"></div>
                    <div class="month-value">${month.contracts_count} عقد</div>
                    <div class="month-amount">${this.formatCurrency(month.total_amount)}</div>
                </div>
            `;
        });

        chartHTML += '</div>';
        return chartHTML;
    }

    // طباعة التقرير
    printReport() {
        window.print();
    }

    // تصدير التقرير
    exportReport(format) {
        showAlert(`ميزة تصدير ${format} قيد التطوير`, 'info');
        // TODO: تطوير ميزة التصدير
    }

    // تنسيق العملة
    formatCurrency(amount) {
        if (!amount || amount === 0) return '0 د.ك';
        return `${parseFloat(amount).toLocaleString()} د.ك`;
    }

    // تنسيق التاريخ
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    // تنسيق الشهر
    formatMonth(monthString) {
        if (!monthString) return '-';
        const [year, month] = monthString.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }

    // حساب الأيام المتبقية
    getDaysRemaining(endDate) {
        if (!endDate) return null;
        
        const today = new Date();
        const end = new Date(endDate);
        const diffTime = end - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return diffDays;
    }

    // تقرير العقود المنتهية
    async showExpiringContractsReport() {
        await this.loadReportData();
        
        const content = `
            <div class="report-header">
                <h2>تقرير العقود المنتهية قريباً</h2>
                <p class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>
            
            <div class="report-content">
                <div class="report-section">
                    <h3>العقود المنتهية خلال 30 يوم القادمة</h3>
                    ${this.renderExpiringContractsTable()}
                </div>
            </div>

            <div class="report-actions">
                <button class="btn btn-primary" onclick="reportsManager.printReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
            </div>
        `;

        showModal(content, 'large');
    }

    // تقرير الموردين
    async showSuppliersReport() {
        await this.loadReportData();
        
        const content = `
            <div class="report-header">
                <h2>تقرير الموردين</h2>
                <p class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>
            
            <div class="report-content">
                <div class="report-section">
                    <h3>أفضل الموردين حسب عدد العقود</h3>
                    ${this.renderTopSuppliersTable()}
                </div>
            </div>

            <div class="report-actions">
                <button class="btn btn-primary" onclick="reportsManager.printReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
            </div>
        `;

        showModal(content, 'large');
    }
}

// إنشاء مثيل من مدير التقارير
const reportsManager = new ReportsManager();
