const Database = require('better-sqlite3');
const path = require('path');

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, 'suppliers.db');
    }

    // تهيئة قاعدة البيانات
    initialize() {
        try {
            this.db = new Database(this.dbPath);
            this.createTables();
            this.createIndexes();
            this.insertSampleData();
            console.log('تم تهيئة قاعدة البيانات بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة قاعدة البيانات:', error);
            throw error;
        }
    }

    // إنشاء الجداول
    createTables() {
        const tables = [
            // جدول الموردين
            `CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                email TEXT,
                activity_type TEXT CHECK(activity_type IN ('استهلاكي', 'غذائي')),
                registration_date DATE DEFAULT CURRENT_DATE,
                status TEXT DEFAULT 'نشط' CHECK(status IN ('نشط', 'غير نشط')),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول العقود
            `CREATE TABLE IF NOT EXISTS contracts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER NOT NULL,
                contract_type TEXT NOT NULL CHECK(contract_type IN ('طبليات', 'قواطع', 'جندولات', 'استنادات')),
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                status TEXT DEFAULT 'نشط' CHECK(status IN ('نشط', 'منتهي', 'معلق')),
                total_amount DECIMAL(10,2),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id) ON DELETE CASCADE
            )`,

            // جدول الطبليات والمساحات
            `CREATE TABLE IF NOT EXISTS tables_spaces (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                table_number TEXT NOT NULL,
                area_sqm DECIMAL(8,2) NOT NULL,
                monthly_price DECIMAL(10,2) NOT NULL,
                location TEXT,
                status TEXT DEFAULT 'مؤجرة' CHECK(status IN ('مؤجرة', 'شاغرة')),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (contract_id) REFERENCES contracts (id) ON DELETE CASCADE
            )`,

            // جدول المدفوعات
            `CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_date DATE,
                due_date DATE NOT NULL,
                status TEXT DEFAULT 'مستحق' CHECK(status IN ('مدفوع', 'مستحق', 'متأخر')),
                payment_method TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (contract_id) REFERENCES contracts (id) ON DELETE CASCADE
            )`,

            // جدول أنواع الأنشطة (للمرجعية)
            `CREATE TABLE IF NOT EXISTS activity_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                base_price DECIMAL(10,2),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // جدول المواقع في السوق
            `CREATE TABLE IF NOT EXISTS market_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT NOT NULL,
                description TEXT,
                total_tables INTEGER DEFAULT 0,
                available_tables INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        tables.forEach(tableSQL => {
            this.db.exec(tableSQL);
        });
    }

    // إنشاء الفهارس لتحسين الأداء
    createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)',
            'CREATE INDEX IF NOT EXISTS idx_suppliers_activity ON suppliers(activity_type)',
            'CREATE INDEX IF NOT EXISTS idx_contracts_supplier ON contracts(supplier_id)',
            'CREATE INDEX IF NOT EXISTS idx_contracts_dates ON contracts(start_date, end_date)',
            'CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status)',
            'CREATE INDEX IF NOT EXISTS idx_tables_contract ON tables_spaces(contract_id)',
            'CREATE INDEX IF NOT EXISTS idx_tables_number ON tables_spaces(table_number)',
            'CREATE INDEX IF NOT EXISTS idx_payments_contract ON payments(contract_id)',
            'CREATE INDEX IF NOT EXISTS idx_payments_due ON payments(due_date, status)',
            'CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)'
        ];

        indexes.forEach(indexSQL => {
            this.db.exec(indexSQL);
        });
    }

    // إدراج بيانات تجريبية
    insertSampleData() {
        // التحقق من وجود بيانات
        const supplierCount = this.db.prepare('SELECT COUNT(*) as count FROM suppliers').get().count;
        
        if (supplierCount > 0) {
            return; // البيانات موجودة بالفعل
        }

        // إدراج أنواع الأنشطة
        const insertActivityType = this.db.prepare(`
            INSERT INTO activity_types (name, description, base_price) 
            VALUES (?, ?, ?)
        `);

        const activityTypes = [
            ['استهلاكي', 'بيع المواد الاستهلاكية والمنزلية', 150.00],
            ['غذائي', 'بيع المواد الغذائية والمشروبات', 200.00]
        ];

        activityTypes.forEach(type => {
            insertActivityType.run(type);
        });

        // إدراج مواقع السوق
        const insertLocation = this.db.prepare(`
            INSERT INTO market_locations (section_name, description, total_tables, available_tables) 
            VALUES (?, ?, ?, ?)
        `);

        const locations = [
            ['القسم الشمالي', 'قسم المواد الاستهلاكية', 50, 10],
            ['القسم الجنوبي', 'قسم المواد الغذائية', 40, 5],
            ['القسم الشرقي', 'قسم الخضروات والفواكه', 30, 8],
            ['القسم الغربي', 'قسم الملابس والأقمشة', 35, 12]
        ];

        locations.forEach(location => {
            insertLocation.run(location);
        });

        // إدراج موردين تجريبيين
        const insertSupplier = this.db.prepare(`
            INSERT INTO suppliers (name, phone, address, email, activity_type, notes) 
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        const sampleSuppliers = [
            ['أحمد محمد الكندري', '99887766', 'المنقف - قطعة 1', '<EMAIL>', 'استهلاكي', 'مورد موثوق'],
            ['فاطمة علي العتيبي', '99776655', 'المنقف - قطعة 2', '<EMAIL>', 'غذائي', 'متخصصة في المواد الغذائية'],
            ['محمد سالم المطيري', '99665544', 'المنقف - قطعة 3', '<EMAIL>', 'استهلاكي', 'تاجر خبرة 10 سنوات']
        ];

        sampleSuppliers.forEach(supplier => {
            insertSupplier.run(supplier);
        });

        console.log('تم إدراج البيانات التجريبية بنجاح');
    }

    // تنفيذ استعلام
    query(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.all(params);
        } catch (error) {
            console.error('خطأ في الاستعلام:', error);
            throw error;
        }
    }

    // تنفيذ أمر (INSERT, UPDATE, DELETE)
    run(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.run(params);
        } catch (error) {
            console.error('خطأ في تنفيذ الأمر:', error);
            throw error;
        }
    }

    // الحصول على سجل واحد
    get(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.get(params);
        } catch (error) {
            console.error('خطأ في الحصول على السجل:', error);
            throw error;
        }
    }

    // تنفيذ معاملة (Transaction)
    transaction(callback) {
        const transaction = this.db.transaction(callback);
        return transaction;
    }

    // إغلاق قاعدة البيانات
    close() {
        if (this.db) {
            this.db.close();
            console.log('تم إغلاق قاعدة البيانات');
        }
    }

    // نسخ احتياطي لقاعدة البيانات
    backup(backupPath) {
        try {
            this.db.backup(backupPath);
            console.log(`تم إنشاء نسخة احتياطية في: ${backupPath}`);
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            throw error;
        }
    }

    // إحصائيات قاعدة البيانات
    getStats() {
        const stats = {
            suppliers: this.get('SELECT COUNT(*) as count FROM suppliers').count,
            activeSuppliers: this.get('SELECT COUNT(*) as count FROM suppliers WHERE status = "نشط"').count,
            contracts: this.get('SELECT COUNT(*) as count FROM contracts').count,
            activeContracts: this.get('SELECT COUNT(*) as count FROM contracts WHERE status = "نشط"').count,
            tables: this.get('SELECT COUNT(*) as count FROM tables_spaces').count,
            rentedTables: this.get('SELECT COUNT(*) as count FROM tables_spaces WHERE status = "مؤجرة"').count,
            payments: this.get('SELECT COUNT(*) as count FROM payments').count,
            pendingPayments: this.get('SELECT COUNT(*) as count FROM payments WHERE status = "مستحق"').count
        };

        return stats;
    }
}

module.exports = DatabaseManager;
