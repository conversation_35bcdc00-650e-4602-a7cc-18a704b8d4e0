const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');
const DatabaseManager = require('./database');

let mainWindow;
let dbManager;

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1000,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: 'إدارة عقود الموردين - جمعية المنقف'
  });

  mainWindow.loadFile('src/index.html');

  // فتح أدوات المطور في وضع التطوير
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// إعداد قاعدة البيانات
function initDatabase() {
  try {
    dbManager = new DatabaseManager();
    dbManager.initialize();
    console.log('تم إنشاء قاعدة البيانات بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء قاعدة البيانات:', error);
  }
}

// إعداد القائمة العربية
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-action', 'new');
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('menu-action', 'save');
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        {
          label: 'إعادة تحميل',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.reload();
          }
        },
        {
          label: 'أدوات المطور',
          accelerator: 'F12',
          click: () => {
            mainWindow.webContents.toggleDevTools();
          }
        }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            mainWindow.webContents.send('menu-action', 'about');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// معالجات IPC للتفاعل مع قاعدة البيانات
ipcMain.handle('db-query', async (event, query, params = []) => {
  try {
    return dbManager.query(query, params);
  } catch (error) {
    console.error('خطأ في الاستعلام:', error);
    throw error;
  }
});

ipcMain.handle('db-run', async (event, query, params = []) => {
  try {
    return dbManager.run(query, params);
  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error);
    throw error;
  }
});

ipcMain.handle('db-get', async (event, query, params = []) => {
  try {
    return dbManager.get(query, params);
  } catch (error) {
    console.error('خطأ في الحصول على السجل:', error);
    throw error;
  }
});

ipcMain.handle('db-stats', async (event) => {
  try {
    return dbManager.getStats();
  } catch (error) {
    console.error('خطأ في الحصول على الإحصائيات:', error);
    throw error;
  }
});

// أحداث التطبيق
app.whenReady().then(() => {
  initDatabase();
  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (dbManager) {
      dbManager.close();
    }
    app.quit();
  }
});

app.on('before-quit', () => {
  if (dbManager) {
    dbManager.close();
  }
});
