/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    direction: rtl;
    overflow-x: hidden;
}

/* الحاوي الرئيسي */
.app-container {
    display: flex;
    height: 100vh;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1000;
}

.sidebar-header {
    padding: 30px 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 300;
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    border-right: 3px solid transparent;
}

.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-right-color: #fff;
}

.menu-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-right-color: #fff;
}

.menu-item i {
    margin-left: 15px;
    font-size: 1.1rem;
    width: 20px;
}

.menu-item span {
    font-weight: 500;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* رأس الصفحة */
.main-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.header-left h1 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 5px;
    font-weight: 700;
}

.header-left p {
    color: #6c757d;
    font-size: 0.9rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6c757d;
    font-weight: 500;
}

.user-info i {
    font-size: 1.5rem;
    color: #667eea;
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* محتوى الصفحة */
.page-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: #f8f9fa;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #6c757d;
    font-weight: 500;
}

/* أقسام لوحة التحكم */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.dashboard-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

/* إجراءات الصفحة */
.page-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    position: relative;
    max-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.filter-group {
    display: flex;
    gap: 15px;
}

.filter-group select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    background: white;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

th,
td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

th {
    font-weight: 600;
    font-size: 0.9rem;
}

tbody tr:hover {
    background-color: #f8f9fa;
}

/* الحالات */
.status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status.active {
    background: #d4edda;
    color: #155724;
}

.status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.status.pending {
    background: #fff3cd;
    color: #856404;
}

/* النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* رسائل التنبيه */
.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .sidebar-menu {
        display: flex;
        overflow-x: auto;
        padding: 10px;
    }

    .menu-item {
        min-width: 120px;
        text-align: center;
        flex-direction: column;
        gap: 5px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .page-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

/* تحسينات الموردين */
.supplier-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.supplier-info strong {
    font-weight: 600;
    color: #2c3e50;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #34495e;
}

.contact-info i {
    color: #3498db;
    width: 16px;
}

.activity-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
}

.activity-استهلاكي {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.activity-غذائي {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #ce93d8;
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تفاصيل المورد */
.supplier-details {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px 0;
}

.detail-row {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row strong {
    min-width: 120px;
    color: #2c3e50;
    font-weight: 600;
}

/* تحسينات الجداول */
.table-container table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
    display: block;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

/* تحسينات العقود */
.contract-supplier {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contract-supplier strong {
    font-weight: 600;
    color: #2c3e50;
}

.contract-type-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
}

.type-طبليات {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.type-قواطع {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc02;
}

.type-جندولات {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #ce93d8;
}

.type-استنادات {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.end-date-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.days-remaining {
    font-size: 0.8rem;
    color: #666;
}

.days-remaining.warning {
    color: #f57c00;
    font-weight: 600;
}

.amount-info {
    font-weight: 600;
    color: #2c3e50;
}

/* حالات العقود */
.status.active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.expired {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.suspended {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* تحسينات النماذج */
.form-row {
    display: flex;
    gap: 16px;
}

.form-row .form-group {
    flex: 1;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
}

/* تحسينات البحث والتصفية */
.search-filters {
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;
}

.search-input {
    padding: 10px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.search-input:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group select,
.filter-group input[type="date"] {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.9rem;
    background: white;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input[type="date"]:focus {
    border-color: #3498db;
    outline: none;
}

.filter-group input[type="date"] {
    min-width: 140px;
}

/* تحسينات الصفحات */
.page-actions {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    gap: 20px;
}

@media (max-width: 768px) {
    .page-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filters {
        width: 100%;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group select,
    .filter-group input[type="date"] {
        width: 100%;
    }
}

/* تنسيقات التقارير */
.report-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
}

.report-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.report-date {
    color: #7f8c8d;
    font-size: 1rem;
    margin: 0;
}

.report-content {
    margin-bottom: 30px;
}

.report-section {
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.report-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 8px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.pie-chart {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chart-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
}

.chart-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
}

.chart-color.consumer {
    background-color: #3498db;
}

.chart-color.food {
    background-color: #e74c3c;
}

.bar-chart {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chart-bar {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bar-label {
    min-width: 80px;
    font-weight: 500;
}

.bar-value {
    background: #3498db;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-table th,
.report-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e0e0e0;
}

.report-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.report-table tbody tr:hover {
    background: #f8f9fa;
}

.monthly-chart {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    padding: 20px 0;
}

.month-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
    gap: 8px;
}

.month-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #2c3e50;
}

.month-bar {
    width: 30px;
    background: #3498db;
    border-radius: 4px 4px 0 0;
    min-height: 20px;
}

.month-value {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.month-amount {
    font-size: 0.8rem;
    color: #27ae60;
    font-weight: 500;
}

.report-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 20px;
    border-top: 2px solid #e0e0e0;
}

.text-danger {
    color: #e74c3c !important;
}

.text-warning {
    color: #f39c12 !important;
}

.text-muted {
    color: #7f8c8d !important;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* تحسينات الطباعة */
@media print {
    .report-actions {
        display: none;
    }

    .modal-overlay {
        position: static !important;
        background: none !important;
    }

    .modal {
        box-shadow: none !important;
        max-width: none !important;
        margin: 0 !important;
    }

    .report-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .report-actions {
        flex-direction: column;
    }

    .monthly-chart {
        justify-content: center;
    }

    .chart-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* بطاقات التقارير */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.report-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
}

.report-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.report-info {
    flex: 1;
}

.report-info h3 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.report-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.4;
}

.report-action {
    color: #3498db;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.report-card:hover .report-action {
    transform: translateX(-5px);
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin: 0;
}

@media (max-width: 768px) {
    .reports-grid {
        grid-template-columns: 1fr;
    }

    .report-card {
        padding: 20px;
        gap: 15px;
    }

    .report-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .page-header h2 {
        font-size: 1.6rem;
        flex-direction: column;
        gap: 10px;
    }
}

/* تحسينات لوحة التحكم */
.dashboard-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.section-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.mini-chart {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #7f8c8d;
    font-style: italic;
}

.dashboard-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 30px 0;
    border-top: 2px solid #e0e0e0;
    margin-top: 30px;
}

.dashboard-actions .btn {
    padding: 12px 24px;
    font-size: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.dashboard-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
    .dashboard-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .dashboard-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .dashboard-actions .btn {
        text-align: center;
    }
}

/* رسوم بيانية لوحة التحكم */
.distribution-chart {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.distribution-chart .chart-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.chart-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.bar-fill.type-طبليات {
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.bar-fill.type-قواطع {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.bar-fill.type-جندولات {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.bar-fill.type-استنادات {
    background: linear-gradient(90deg, #27ae60, #229954);
}

.chart-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.type-name {
    font-weight: 500;
    color: #2c3e50;
}

.type-count {
    color: #7f8c8d;
    font-size: 0.8rem;
}