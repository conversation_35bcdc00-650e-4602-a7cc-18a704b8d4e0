{"name": "supplier-management-app", "version": "1.0.0", "description": "تطبيق إدارة عقود الموردين - جمعية المنقف", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "suppliers", "contracts", "management", "kuwait"], "author": "جمعية المنقف", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"sqlite3": "^5.1.6", "better-sqlite3": "^9.2.2"}, "build": {"appId": "com.almanqaf.supplier-management", "productName": "إدارة عقود الموردين", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/better-sqlite3/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}