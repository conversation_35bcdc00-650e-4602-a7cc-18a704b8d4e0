// إدارة العقود - وظائف متقدمة

class ContractsManager {
    constructor() {
        this.contracts = [];
        this.suppliers = [];
        this.currentEditingId = null;
    }

    // تحميل جميع العقود
    async loadContracts() {
        try {
            const query = `
                SELECT c.*, s.name as supplier_name, s.activity_type 
                FROM contracts c 
                LEFT JOIN suppliers s ON c.supplier_id = s.id 
                ORDER BY c.start_date DESC
            `;
            this.contracts = await ipcRenderer.invoke('db-query', query);
            this.renderContractsTable();
            this.updateContractsStats();
        } catch (error) {
            console.error('خطأ في تحميل العقود:', error);
            showAlert('حدث خطأ في تحميل بيانات العقود', 'danger');
        }
    }

    // تحميل الموردين للاختيار
    async loadSuppliersForSelection() {
        try {
            this.suppliers = await ipcRenderer.invoke('db-query',
                'SELECT id, name, activity_type FROM suppliers WHERE status = "نشط" ORDER BY name');
        } catch (error) {
            console.error('خطأ في تحميل الموردين:', error);
        }
    }

    // عرض جدول العقود مع تحسينات
    renderContractsTable() {
        const tbody = document.querySelector('#contracts-table tbody');
        tbody.innerHTML = '';

        if (this.contracts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px;">
                        <i class="fas fa-file-contract" style="font-size: 3rem; color: #ccc; margin-bottom: 10px;"></i>
                        <p>لا توجد عقود مسجلة بعد</p>
                        <button class="btn btn-primary" onclick="contractsManager.showAddContractModal()">
                            <i class="fas fa-plus"></i> إضافة أول عقد
                        </button>
                    </td>
                </tr>
            `;
            return;
        }

        this.contracts.forEach(contract => {
            const row = document.createElement('tr');
            const statusClass = this.getContractStatusClass(contract);
            const daysRemaining = this.getDaysRemaining(contract.end_date);

            row.innerHTML = `
                <td>
                    <div class="contract-supplier">
                        <strong>${contract.supplier_name || 'غير محدد'}</strong>
                        ${contract.activity_type ? `<br><small class="text-muted">${contract.activity_type}</small>` : ''}
                    </div>
                </td>
                <td>
                    <span class="contract-type-badge type-${contract.contract_type}">
                        ${contract.contract_type}
                    </span>
                </td>
                <td>${this.formatDate(contract.start_date)}</td>
                <td>
                    <div class="end-date-info">
                        ${this.formatDate(contract.end_date)}
                        ${daysRemaining !== null ? `<br><small class="days-remaining ${daysRemaining < 30 ? 'warning' : ''}">${daysRemaining} يوم متبقي</small>` : ''}
                    </div>
                </td>
                <td>
                    <div class="amount-info">
                        ${contract.total_amount ? `${parseFloat(contract.total_amount).toLocaleString()} د.ك` : '-'}
                    </div>
                </td>
                <td>
                    <span class="status ${statusClass}">
                        ${contract.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="contractsManager.editContract(${contract.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="contractsManager.viewContractDetails(${contract.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="contractsManager.manageTableSpaces(${contract.id})" title="إدارة الطبليات">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="contractsManager.toggleContractStatus(${contract.id})" title="تغيير الحالة">
                            <i class="fas fa-toggle-on"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="contractsManager.deleteContract(${contract.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // إضافة عقد جديد
    async addContract(contractData) {
        try {
            // التحقق من صحة البيانات
            if (!this.validateContractData(contractData)) {
                return false;
            }

            const query = `
                INSERT INTO contracts (supplier_id, contract_type, start_date, end_date, total_amount, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            `;

            const result = await ipcRenderer.invoke('db-run', query, [
                contractData.supplier_id,
                contractData.contract_type,
                contractData.start_date,
                contractData.end_date,
                contractData.total_amount,
                contractData.notes
            ]);

            showAlert('تم إضافة العقد بنجاح', 'success');
            await this.loadContracts();
            return result.lastInsertRowid;

        } catch (error) {
            console.error('خطأ في إضافة العقد:', error);
            showAlert('حدث خطأ في إضافة العقد', 'danger');
            return false;
        }
    }

    // إظهار نافذة إضافة عقد
    async showAddContractModal() {
        await this.loadSuppliersForSelection();

        if (this.suppliers.length === 0) {
            showAlert('يجب إضافة موردين أولاً قبل إنشاء العقود', 'warning');
            return;
        }

        const suppliersOptions = this.suppliers.map(supplier =>
            `<option value="${supplier.id}">${supplier.name} - ${supplier.activity_type}</option>`
        ).join('');

        const content = `
            <h3>إضافة عقد جديد</h3>
            <form id="add-contract-form">
                <div class="form-group">
                    <label for="contract-supplier">المورد *</label>
                    <select id="contract-supplier" name="supplier_id" required>
                        <option value="">اختر المورد</option>
                        ${suppliersOptions}
                    </select>
                </div>
                <div class="form-group">
                    <label for="contract-type">نوع العقد *</label>
                    <select id="contract-type" name="contract_type" required>
                        <option value="">اختر نوع العقد</option>
                        <option value="طبليات">طبليات</option>
                        <option value="قواطع">قواطع</option>
                        <option value="جندولات">جندولات</option>
                        <option value="استنادات">استنادات</option>
                    </select>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="contract-start-date">تاريخ البداية *</label>
                        <input type="date" id="contract-start-date" name="start_date" required>
                    </div>
                    <div class="form-group">
                        <label for="contract-end-date">تاريخ الانتهاء *</label>
                        <input type="date" id="contract-end-date" name="end_date" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="contract-amount">المبلغ الإجمالي (د.ك)</label>
                    <input type="number" id="contract-amount" name="total_amount" step="0.01" min="0" placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="contract-notes">ملاحظات</label>
                    <textarea id="contract-notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ العقد</button>
                </div>
            </form>
        `;

        showModal(content);

        // تعيين التاريخ الافتراضي
        document.getElementById('contract-start-date').value = new Date().toISOString().split('T')[0];

        // إضافة مستمع الحدث للنموذج
        document.getElementById('add-contract-form').addEventListener('submit', (e) => {
            this.handleAddContract(e);
        });
    }

    // معالج إضافة العقد
    async handleAddContract(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const contractData = Object.fromEntries(formData.entries());

        const contractId = await this.addContract(contractData);
        if (contractId) {
            closeModal();

            // سؤال المستخدم إذا كان يريد إضافة طبليات للعقد
            if (contractData.contract_type === 'طبليات') {
                const addTables = confirm('هل تريد إضافة طبليات لهذا العقد الآن؟');
                if (addTables) {
                    this.manageTableSpaces(contractId);
                }
            }
        }
    }

    // تعديل عقد
    async editContract(id) {
        try {
            const contract = await ipcRenderer.invoke('db-get', 'SELECT * FROM contracts WHERE id = ?', [id]);

            if (!contract) {
                showAlert('لم يتم العثور على العقد', 'danger');
                return;
            }

            this.currentEditingId = id;
            await this.showEditContractModal(contract);

        } catch (error) {
            console.error('خطأ في تحميل بيانات العقد:', error);
            showAlert('حدث خطأ في تحميل بيانات العقد', 'danger');
        }
    }

    // إظهار نافذة تعديل العقد
    async showEditContractModal(contract) {
        await this.loadSuppliersForSelection();

        const suppliersOptions = this.suppliers.map(supplier =>
            `<option value="${supplier.id}" ${supplier.id === contract.supplier_id ? 'selected' : ''}>${supplier.name} - ${supplier.activity_type}</option>`
        ).join('');

        const content = `
            <h3>تعديل العقد</h3>
            <form id="edit-contract-form">
                <div class="form-group">
                    <label for="edit-contract-supplier">المورد *</label>
                    <select id="edit-contract-supplier" name="supplier_id" required>
                        <option value="">اختر المورد</option>
                        ${suppliersOptions}
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-contract-type">نوع العقد *</label>
                    <select id="edit-contract-type" name="contract_type" required>
                        <option value="">اختر نوع العقد</option>
                        <option value="طبليات" ${contract.contract_type === 'طبليات' ? 'selected' : ''}>طبليات</option>
                        <option value="قواطع" ${contract.contract_type === 'قواطع' ? 'selected' : ''}>قواطع</option>
                        <option value="جندولات" ${contract.contract_type === 'جندولات' ? 'selected' : ''}>جندولات</option>
                        <option value="استنادات" ${contract.contract_type === 'استنادات' ? 'selected' : ''}>استنادات</option>
                    </select>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit-contract-start-date">تاريخ البداية *</label>
                        <input type="date" id="edit-contract-start-date" name="start_date" value="${contract.start_date}" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-contract-end-date">تاريخ الانتهاء *</label>
                        <input type="date" id="edit-contract-end-date" name="end_date" value="${contract.end_date}" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="edit-contract-status">الحالة</label>
                    <select id="edit-contract-status" name="status">
                        <option value="نشط" ${contract.status === 'نشط' ? 'selected' : ''}>نشط</option>
                        <option value="منتهي" ${contract.status === 'منتهي' ? 'selected' : ''}>منتهي</option>
                        <option value="معلق" ${contract.status === 'معلق' ? 'selected' : ''}>معلق</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-contract-amount">المبلغ الإجمالي (د.ك)</label>
                    <input type="number" id="edit-contract-amount" name="total_amount" step="0.01" min="0" value="${contract.total_amount || ''}">
                </div>
                <div class="form-group">
                    <label for="edit-contract-notes">ملاحظات</label>
                    <textarea id="edit-contract-notes" name="notes" rows="3">${contract.notes || ''}</textarea>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        `;

        showModal(content);

        // إضافة مستمع الحدث للنموذج
        document.getElementById('edit-contract-form').addEventListener('submit', (e) => {
            this.handleEditContract(e);
        });
    }

    // معالج تعديل العقد
    async handleEditContract(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const contractData = Object.fromEntries(formData.entries());

        try {
            // التحقق من صحة البيانات
            if (!this.validateContractData(contractData)) {
                return;
            }

            const query = `
                UPDATE contracts 
                SET supplier_id = ?, contract_type = ?, start_date = ?, end_date = ?, 
                    status = ?, total_amount = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;

            await ipcRenderer.invoke('db-run', query, [
                contractData.supplier_id,
                contractData.contract_type,
                contractData.start_date,
                contractData.end_date,
                contractData.status,
                contractData.total_amount,
                contractData.notes,
                this.currentEditingId
            ]);

            showAlert('تم تحديث العقد بنجاح', 'success');
            closeModal();
            await this.loadContracts();
            this.currentEditingId = null;

        } catch (error) {
            console.error('خطأ في تحديث العقد:', error);
            showAlert('حدث خطأ في تحديث العقد', 'danger');
        }
    }

    // التحقق من صحة بيانات العقد
    validateContractData(data) {
        if (!data.supplier_id) {
            showAlert('يجب اختيار المورد', 'warning');
            return false;
        }

        if (!data.contract_type) {
            showAlert('يجب اختيار نوع العقد', 'warning');
            return false;
        }

        if (!data.start_date || !data.end_date) {
            showAlert('يجب تحديد تواريخ العقد', 'warning');
            return false;
        }

        if (new Date(data.start_date) >= new Date(data.end_date)) {
            showAlert('تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية', 'warning');
            return false;
        }

        if (data.total_amount && parseFloat(data.total_amount) < 0) {
            showAlert('المبلغ يجب أن يكون أكبر من أو يساوي صفر', 'warning');
            return false;
        }

        return true;
    }

    // الحصول على فئة حالة العقد
    getContractStatusClass(contract) {
        switch (contract.status) {
            case 'نشط': return 'active';
            case 'منتهي': return 'expired';
            case 'معلق': return 'suspended';
            default: return '';
        }
    }

    // حساب الأيام المتبقية
    getDaysRemaining(endDate) {
        if (!endDate) return null;

        const today = new Date();
        const end = new Date(endDate);
        const diffTime = end - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays;
    }

    // تنسيق التاريخ
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    // تحديث إحصائيات العقود
    updateContractsStats() {
        const totalContracts = this.contracts.length;
        const activeContracts = this.contracts.filter(c => c.status === 'نشط').length;
        const expiredContracts = this.contracts.filter(c => c.status === 'منتهي').length;
        const suspendedContracts = this.contracts.filter(c => c.status === 'معلق').length;

        // تحديث العناصر في الواجهة إذا كانت موجودة
        const statsElements = {
            'total-contracts': totalContracts,
            'active-contracts': activeContracts,
            'expired-contracts': expiredContracts,
            'suspended-contracts': suspendedContracts
        };

        Object.entries(statsElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    // البحث والتصفية المتقدمة
    filterContracts(searchTerm = '', contractType = '', status = '', dateFrom = '', dateTo = '') {
        let filteredContracts = [...this.contracts];

        // البحث النصي
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            filteredContracts = filteredContracts.filter(contract =>
                (contract.supplier_name && contract.supplier_name.toLowerCase().includes(term)) ||
                (contract.contract_type && contract.contract_type.toLowerCase().includes(term)) ||
                (contract.notes && contract.notes.toLowerCase().includes(term))
            );
        }

        // تصفية حسب نوع العقد
        if (contractType) {
            filteredContracts = filteredContracts.filter(contract =>
                contract.contract_type === contractType
            );
        }

        // تصفية حسب الحالة
        if (status) {
            filteredContracts = filteredContracts.filter(contract =>
                contract.status === status
            );
        }

        // تصفية حسب التاريخ
        if (dateFrom) {
            filteredContracts = filteredContracts.filter(contract =>
                new Date(contract.start_date) >= new Date(dateFrom)
            );
        }

        if (dateTo) {
            filteredContracts = filteredContracts.filter(contract =>
                new Date(contract.end_date) <= new Date(dateTo)
            );
        }

        // عرض النتائج المفلترة
        this.renderFilteredContracts(filteredContracts);
    }

    // عرض العقود المفلترة
    renderFilteredContracts(filteredContracts) {
        const tbody = document.querySelector('#contracts-table tbody');
        tbody.innerHTML = '';

        if (filteredContracts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px;">
                        <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 10px;"></i>
                        <p>لا توجد عقود تطابق معايير البحث</p>
                        <button class="btn btn-secondary" onclick="contractsManager.clearFilters()">
                            <i class="fas fa-times"></i> مسح الفلاتر
                        </button>
                    </td>
                </tr>
            `;
            return;
        }

        filteredContracts.forEach(contract => {
            const row = document.createElement('tr');
            const statusClass = this.getContractStatusClass(contract);
            const daysRemaining = this.getDaysRemaining(contract.end_date);

            row.innerHTML = `
                <td>
                    <div class="contract-supplier">
                        <strong>${contract.supplier_name || 'غير محدد'}</strong>
                        ${contract.activity_type ? `<br><small class="text-muted">${contract.activity_type}</small>` : ''}
                    </div>
                </td>
                <td>
                    <span class="contract-type-badge type-${contract.contract_type}">
                        ${contract.contract_type}
                    </span>
                </td>
                <td>${this.formatDate(contract.start_date)}</td>
                <td>
                    <div class="end-date-info">
                        ${this.formatDate(contract.end_date)}
                        ${daysRemaining !== null ? `<br><small class="days-remaining ${daysRemaining < 30 ? 'warning' : ''}">${daysRemaining} يوم متبقي</small>` : ''}
                    </div>
                </td>
                <td>
                    <div class="amount-info">
                        ${contract.total_amount ? `${parseFloat(contract.total_amount).toLocaleString()} د.ك` : '-'}
                    </div>
                </td>
                <td>
                    <span class="status ${statusClass}">
                        ${contract.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="contractsManager.editContract(${contract.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="contractsManager.viewContractDetails(${contract.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="contractsManager.manageTableSpaces(${contract.id})" title="إدارة الطبليات">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="contractsManager.toggleContractStatus(${contract.id})" title="تغيير الحالة">
                            <i class="fas fa-toggle-on"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="contractsManager.deleteContract(${contract.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // مسح الفلاتر
    clearFilters() {
        // مسح قيم البحث والفلاتر
        const searchInput = document.getElementById('contracts-search');
        const typeFilter = document.getElementById('contract-type-filter');
        const statusFilter = document.getElementById('contract-status-filter');
        const dateFromFilter = document.getElementById('contract-date-from');
        const dateToFilter = document.getElementById('contract-date-to');

        if (searchInput) searchInput.value = '';
        if (typeFilter) typeFilter.value = '';
        if (statusFilter) statusFilter.value = '';
        if (dateFromFilter) dateFromFilter.value = '';
        if (dateToFilter) dateToFilter.value = '';

        // إعادة عرض جميع العقود
        this.renderContractsTable();
    }

    // حذف عقد
    async deleteContract(id) {
        if (!confirm('هل أنت متأكد من حذف هذا العقد؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            return;
        }

        try {
            await ipcRenderer.invoke('db-run', 'DELETE FROM contracts WHERE id = ?', [id]);
            showAlert('تم حذف العقد بنجاح', 'success');
            await this.loadContracts();
        } catch (error) {
            console.error('خطأ في حذف العقد:', error);
            showAlert('حدث خطأ في حذف العقد', 'danger');
        }
    }

    // تغيير حالة العقد
    async toggleContractStatus(id) {
        try {
            const contract = await ipcRenderer.invoke('db-get', 'SELECT * FROM contracts WHERE id = ?', [id]);

            if (!contract) {
                showAlert('لم يتم العثور على العقد', 'danger');
                return;
            }

            let newStatus;
            switch (contract.status) {
                case 'نشط':
                    newStatus = 'معلق';
                    break;
                case 'معلق':
                    newStatus = 'نشط';
                    break;
                case 'منتهي':
                    newStatus = 'نشط';
                    break;
                default:
                    newStatus = 'نشط';
            }

            await ipcRenderer.invoke('db-run',
                'UPDATE contracts SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [newStatus, id]
            );

            showAlert(`تم تغيير حالة العقد إلى "${newStatus}"`, 'success');
            await this.loadContracts();

        } catch (error) {
            console.error('خطأ في تغيير حالة العقد:', error);
            showAlert('حدث خطأ في تغيير حالة العقد', 'danger');
        }
    }

    // عرض تفاصيل العقد
    async viewContractDetails(id) {
        try {
            const query = `
                SELECT c.*, s.name as supplier_name, s.phone, s.email, s.activity_type
                FROM contracts c
                LEFT JOIN suppliers s ON c.supplier_id = s.id
                WHERE c.id = ?
            `;
            const contract = await ipcRenderer.invoke('db-get', query, [id]);

            if (!contract) {
                showAlert('لم يتم العثور على العقد', 'danger');
                return;
            }

            const daysRemaining = this.getDaysRemaining(contract.end_date);
            const statusClass = this.getContractStatusClass(contract);

            const content = `
                <h3>تفاصيل العقد</h3>
                <div class="contract-details">
                    <div class="detail-row">
                        <strong>المورد:</strong>
                        <span>${contract.supplier_name || 'غير محدد'}</span>
                    </div>
                    <div class="detail-row">
                        <strong>نوع النشاط:</strong>
                        <span>${contract.activity_type || '-'}</span>
                    </div>
                    <div class="detail-row">
                        <strong>رقم الهاتف:</strong>
                        <span>${contract.phone || '-'}</span>
                    </div>
                    <div class="detail-row">
                        <strong>البريد الإلكتروني:</strong>
                        <span>${contract.email || '-'}</span>
                    </div>
                    <div class="detail-row">
                        <strong>نوع العقد:</strong>
                        <span class="contract-type-badge type-${contract.contract_type}">${contract.contract_type}</span>
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ البداية:</strong>
                        <span>${this.formatDate(contract.start_date)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ الانتهاء:</strong>
                        <span>${this.formatDate(contract.end_date)} ${daysRemaining !== null ? `(${daysRemaining} يوم متبقي)` : ''}</span>
                    </div>
                    <div class="detail-row">
                        <strong>الحالة:</strong>
                        <span class="status ${statusClass}">${contract.status}</span>
                    </div>
                    <div class="detail-row">
                        <strong>المبلغ الإجمالي:</strong>
                        <span>${contract.total_amount ? `${parseFloat(contract.total_amount).toLocaleString()} د.ك` : 'غير محدد'}</span>
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ الإنشاء:</strong>
                        <span>${this.formatDate(contract.created_at)}</span>
                    </div>
                    ${contract.updated_at ? `
                        <div class="detail-row">
                            <strong>آخر تحديث:</strong>
                            <span>${this.formatDate(contract.updated_at)}</span>
                        </div>
                    ` : ''}
                    ${contract.notes ? `
                        <div class="detail-row">
                            <strong>ملاحظات:</strong>
                            <span>${contract.notes}</span>
                        </div>
                    ` : ''}
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="contractsManager.editContract(${contract.id}); closeModal();">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    ${contract.contract_type === 'طبليات' ? `
                        <button class="btn btn-success" onclick="contractsManager.manageTableSpaces(${contract.id}); closeModal();">
                            <i class="fas fa-table"></i> إدارة الطبليات
                        </button>
                    ` : ''}
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                </div>
            `;

            showModal(content);

        } catch (error) {
            console.error('خطأ في تحميل تفاصيل العقد:', error);
            showAlert('حدث خطأ في تحميل تفاصيل العقد', 'danger');
        }
    }

    // إدارة مساحات الطبليات (سيتم تطويرها لاحقاً)
    async manageTableSpaces(contractId) {
        showAlert('ميزة إدارة الطبليات قيد التطوير', 'info');
        // TODO: تطوير واجهة إدارة الطبليات والمساحات
    }
}

// إنشاء مثيل من مدير العقود
const contractsManager = new ContractsManager();
