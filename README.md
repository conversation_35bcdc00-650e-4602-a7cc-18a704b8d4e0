# تطبيق إدارة عقود الموردين - جمعية المنقف

تطبيق سطح مكتب متطور لإدارة عقود الموردين وتتبع الطبليات والمدفوعات في السوق المركزي لجمعية المنقف.

## المميزات الرئيسية

### 📊 لوحة التحكم
- عرض إحصائيات شاملة للموردين والعقود
- متابعة العقود المنتهية قريباً
- تتبع المدفوعات المتأخرة
- مؤشرات أداء مرئية

### 👥 إدارة الموردين
- تسجيل بيانات الموردين بشكل منظم
- تصنيف الأنشطة (استهلاكي/غذائي)
- معلومات الاتصال والعناوين
- حالة المورد (نشط/غير نشط)

### 📋 إدارة العقود
- إنشاء عقود للطبليات والقواطع والجندولات والاستنادات
- تحديد فترات العقود والمبالغ
- تتبع حالة العقود (نشط/منتهي/معلق)
- ربط العقود بالموردين

### 🔍 البحث والتصفية
- بحث سريع في الموردين
- تصفية العقود حسب النوع والحالة
- فلترة متقدمة للبيانات

### 📈 التقارير والإحصائيات
- تقارير دورية شاملة
- إحصائيات الإيجارات والمدفوعات
- تصدير البيانات للطباعة

## متطلبات النظام

- نظام التشغيل: Windows 10 أو أحدث
- Node.js 16.0 أو أحدث
- مساحة تخزين: 100 ميجابايت على الأقل

## التثبيت والتشغيل

### الطريقة الأولى: تشغيل مباشر (إذا كان Node.js مثبت)

1. تحميل المشروع:
```bash
git clone [repository-url]
cd supplier-management-app
```

2. تثبيت التبعيات:
```bash
npm install
```

3. تشغيل التطبيق:
```bash
npm start
```

### الطريقة الثانية: تشغيل بدون Node.js

إذا لم يكن Node.js مثبت على النظام، يمكن تشغيل التطبيق باستخدام متصفح الويب:

1. فتح ملف `src/index.html` في متصفح حديث
2. ملاحظة: بعض الوظائف قد لا تعمل بشكل كامل بدون Electron

### بناء التطبيق للتوزيع

```bash
npm run build
```

سيتم إنشاء ملف التثبيت في مجلد `dist/`

## هيكل المشروع

```
supplier-management-app/
├── main.js                 # ملف Electron الرئيسي
├── package.json            # إعدادات المشروع والتبعيات
├── README.md              # هذا الملف
├── src/
│   ├── index.html         # الواجهة الرئيسية
│   ├── styles.css         # ملفات التصميم
│   └── app.js             # منطق التطبيق
├── assets/                # الصور والأيقونات
└── suppliers.db           # قاعدة البيانات (تُنشأ تلقائياً)
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

### جدول الموردين (suppliers)
- معرف المورد
- الاسم
- رقم الهاتف
- العنوان
- البريد الإلكتروني
- نوع النشاط
- تاريخ التسجيل
- الحالة

### جدول العقود (contracts)
- معرف العقد
- معرف المورد
- نوع العقد
- تاريخ البداية
- تاريخ الانتهاء
- المبلغ الإجمالي
- الحالة

### جدول الطبليات (tables_spaces)
- معرف الطبلية
- معرف العقد
- رقم الطبلية
- المساحة
- السعر الشهري
- الموقع
- الحالة

### جدول المدفوعات (payments)
- معرف الدفعة
- معرف العقد
- المبلغ
- تاريخ الدفع
- تاريخ الاستحقاق
- حالة الدفع

## الاستخدام

### إضافة مورد جديد
1. انتقل إلى صفحة "الموردين"
2. اضغط على "إضافة مورد جديد"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

### إنشاء عقد جديد
1. تأكد من وجود موردين في النظام
2. انتقل إلى صفحة "العقود"
3. اضغط على "إضافة عقد جديد"
4. اختر المورد ونوع العقد
5. حدد التواريخ والمبلغ
6. اضغط "حفظ"

### البحث والتصفية
- استخدم مربع البحث للعثور على موردين محددين
- استخدم قوائم التصفية لفلترة العقود حسب النوع أو الحالة

## الدعم والصيانة

### النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية من ملف `suppliers.db` بانتظام
- يمكن نسخ الملف إلى مكان آمن أو خدمة تخزين سحابية

### استعادة البيانات
- لاستعادة البيانات، استبدل ملف `suppliers.db` بالنسخة الاحتياطية
- أعد تشغيل التطبيق

### تحديث التطبيق
- احتفظ بنسخة احتياطية من قاعدة البيانات قبل التحديث
- استبدل ملفات التطبيق بالإصدار الجديد
- تأكد من توافق قاعدة البيانات مع الإصدار الجديد

## المساهمة في التطوير

نرحب بالمساهمات لتحسين التطبيق:

1. إنشاء fork للمشروع
2. إنشاء branch جديد للميزة
3. تطبيق التغييرات
4. إرسال pull request

## الترخيص

هذا التطبيق مطور خصيصاً لجمعية المنقف ومرخص تحت رخصة MIT.

## معلومات الاتصال

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: [email]
- الهاتف: [phone]

---

**جمعية المنقف - إدارة عقود الموردين**
*تطوير وتصميم: فريق التطوير*
